<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.zjwly</groupId>
        <artifactId>esop-modules</artifactId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>esop-system</artifactId>

    <description>
        system系统模块
    </description>

    <dependencies>
        <!-- 通用工具-->
        <dependency>
            <groupId>com.zjwly</groupId>
            <artifactId>esop-common-core</artifactId>
        </dependency>

        <dependency>
            <groupId>com.zjwly</groupId>
            <artifactId>esop-common-doc</artifactId>
        </dependency>

        <dependency>
            <groupId>com.zjwly</groupId>
            <artifactId>esop-common-mybatis</artifactId>
        </dependency>

        <dependency>
            <groupId>com.zjwly</groupId>
            <artifactId>esop-common-translation</artifactId>
        </dependency>

        <!-- OSS功能模块 -->
        <dependency>
            <groupId>com.zjwly</groupId>
            <artifactId>esop-common-oss</artifactId>
        </dependency>

        <dependency>
            <groupId>com.zjwly</groupId>
            <artifactId>esop-common-log</artifactId>
        </dependency>

        <!-- excel-->
        <dependency>
            <groupId>com.zjwly</groupId>
            <artifactId>esop-common-excel</artifactId>
        </dependency>

        <!-- SMS功能模块 -->
        <dependency>
            <groupId>com.zjwly</groupId>
            <artifactId>esop-common-sms</artifactId>
        </dependency>

        <dependency>
            <groupId>com.zjwly</groupId>
            <artifactId>esop-common-tenant</artifactId>
        </dependency>

        <dependency>
            <groupId>com.zjwly</groupId>
            <artifactId>esop-common-security</artifactId>
        </dependency>

        <dependency>
            <groupId>com.zjwly</groupId>
            <artifactId>esop-common-web</artifactId>
        </dependency>

        <dependency>
            <groupId>com.zjwly</groupId>
            <artifactId>esop-common-idempotent</artifactId>
        </dependency>

        <dependency>
            <groupId>com.zjwly</groupId>
            <artifactId>esop-common-sensitive</artifactId>
        </dependency>

        <dependency>
            <groupId>com.zjwly</groupId>
            <artifactId>esop-common-encrypt</artifactId>
        </dependency>

        <dependency>
            <groupId>com.zjwly</groupId>
            <artifactId>esop-common-websocket</artifactId>
        </dependency>

        <dependency>
            <groupId>com.zjwly</groupId>
            <artifactId>esop-common-sse</artifactId>
        </dependency>

    </dependencies>

</project>
