package com.zjwly.system.controller.system;

import com.zjwly.common.core.domain.R;
import com.zjwly.common.satoken.utils.LoginHelper;
import com.zjwly.sm.service.SysStationService;
import com.zjwly.system.domain.SysStation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 电站
 *
 * <AUTHOR>
 * @date 2025/05/08 15:15
 */
@RequiredArgsConstructor
@RestController
@RequestMapping("/system/station")
public class SysStationController {
    private final SysStationService sysStationService;

    /**
     * 电站列表
     *
     * @return com.zjwly.common.core.domain.R<java.util.List < com.zjwly.system.domain.SysStation>>
     * <AUTHOR>
     * @date 2025/05/08 15:42
     */
    @GetMapping("/list")
    public R<List<SysStation>> list() {
        return R.ok(sysStationService.list());
    }

    /**
     * 根据用户id获取电站列表
     *
     * @return com.zjwly.common.core.domain.R<java.util.List < com.zjwly.system.domain.SysStation>>
     * <AUTHOR>
     * @date 2025/05/08 15:42
     */
    @GetMapping("/listByUserId")
    public R<List<SysStation>> listByUserId() {
        return R.ok(sysStationService.selectStationListByUserId(LoginHelper.getUserId()));
    }
}
