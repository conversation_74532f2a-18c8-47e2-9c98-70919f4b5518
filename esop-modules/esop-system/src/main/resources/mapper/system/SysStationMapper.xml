<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zjwly.system.mapper.SysStationMapper">
    <select id="selectStationListByUserId" resultType="com.zjwly.system.domain.SysStation">
        SELECT s.*
        FROM sys_station s
                 LEFT JOIN sys_role_station rs ON s.ds = rs.station_ds
                 LEFT JOIN sys_user_role ur ON rs.role_id = ur.role_id
        WHERE ur.user_id = #{userId}
    </select>
</mapper>
