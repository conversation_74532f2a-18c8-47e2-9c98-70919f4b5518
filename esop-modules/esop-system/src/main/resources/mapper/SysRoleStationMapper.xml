<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zjwly.system.mapper.SysRoleStationMapper">
  <resultMap id="BaseResultMap" type="com.zjwly.system.domain.SysRoleStation">
    <!--@mbg.generated-->
    <!--@Table sys_role_station-->
    <id column="role_id" jdbcType="BIGINT" property="roleId" />
    <id column="station_ds" jdbcType="VARCHAR" property="stationDs" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    role_id, station_ds
  </sql>
</mapper>